import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from 'react-oidc-context';
import { toast } from 'react-toastify';
import CustomButton from '../CustomButton';
import ReviewForm, { ReviewData } from '../ReviewForm/ReviewForm';
import { createReview } from '../../../service/reviewService';

const ConfirmationStep = () => {
  const navigate = useNavigate();
  const auth = useAuth();
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);


  const data = useLocation();
  const bookingData = data.state;

  console.log('Booking data: ', bookingData);

  const navigateToLink = (param: string) => {
    if (param === 'dashboard') {
      navigate('/customer/customer-booking');
    } else if (param === 'calender') {
      // navigate('/customer/customer-booking');
      navigate('/');
    }
  };

  const handleAddReview = () => {
    if (!auth.isAuthenticated) {
      toast.error('Please log in to add a review');
      return;
    }
    setShowReviewForm(true);
  };

  const handleSubmitReview = async (reviewData: ReviewData) => {
    if (!bookingData) {
      toast.error('Booking information not available');
      return;
    }

    setIsSubmittingReview(true);
    try {
      const createData = {
        providerId: bookingData.providerId || 'default-provider',
        serviceId: bookingData.serviceId || 'default-service',
        serviceName: bookingData.serviceName || 'Service',
        bookingId: bookingData.bookingId || bookingData.id,
        title: reviewData.title,
        review: reviewData.review,
        comment: reviewData.review,
        rating: reviewData.rating,
        serviceRating: reviewData.serviceRating,
        qualityRating: reviewData.qualityRating,
        valueRating: reviewData.valueRating,
        communicationRating: reviewData.communicationRating,
        timelinessRating: reviewData.timelinessRating,
        imageNames: reviewData.imageNames || [],
        imageUrls: reviewData.imageUrls || [],
        userName: auth.user?.profile?.name || auth.user?.profile?.preferred_username,
        userEmail: auth.user?.profile?.email,
        userProfileImage: auth.user?.profile?.picture,
        isVerified: true,
        date: new Date().toISOString(),
      };

      await createReview(createData);
      toast.success('Review submitted successfully!');
      setShowReviewForm(false);
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review. Please try again.');
    } finally {
      setIsSubmittingReview(false);
    }
  };

  return (
    <div className="flex items-center justify-center bg-gradient-to-b py-12 max-h-screen">
      <div className="w-full max-w-2xl mx-4 text-center transition-all transform bg-white ">
        <div className="flex items-center justify-center w-24 h-24 mx-auto mb-8 bg-green-100 rounded-full">
          <svg
            className="w-12 h-12 text-green-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M5 13l4 4L19 7"
            ></path>
          </svg>
        </div>

        <h1 className="mb-6 text-2xl font-extrabold text-green-600">
          Booking Successful!
        </h1>

        <p className="mb-8 text-md text-gray-700">
          Thank you for your booking. A confirmation email has been sent to your
          registered address. You can view your booking details below.
        </p>

        <div className="space-y-4 sm:space-y-2 rounded-lg border border-gray-100 bg-gray-50 p-6  mb-6 md:mb-8">
          <dl className="sm:flex items-center justify-between gap-4">
            <dt className="font-normal text-sm mb-1 sm:mb-0 text-gray-500 ">
              Booking ID
            </dt>
            <dd className="font-medium text-gray-900 text-sm  sm:text-end">
              {bookingData.bookingId}
            </dd>
          </dl>

          {/* <dl className="sm:flex items-center justify-between gap-4">
            <dt className="font-normal text-sm mb-1 sm:mb-0 text-gray-500 ">
              Booking Status
            </dt>
            <dd className="font-medium text-gray-900 text-sm  sm:text-end">
              {bookingData.bookingStatus}
            </dd>
          </dl>  */}
          <dl className="sm:flex items-center justify-between gap-4">
            <dt className="font-normal text-sm mb-1 sm:mb-0 text-gray-500 ">
              Service Name
            </dt>
            <dd className="font-medium text-gray-900 text-sm  sm:text-end">
              {bookingData.serviceName}
            </dd>
          </dl>
          <dl className="sm:flex items-center justify-between gap-4">
            <dt className="font-normal mb-1 sm:mb-0 text-gray-500 text-sm">
              Customer Name
            </dt>
            <dd className="font-medium text-gray-900  sm:text-end text-sm">
              {bookingData.firstName} {bookingData.lastName}
            </dd>
          </dl>
          <dl className="sm:flex items-center justify-between gap-4">
            <dt className="font-normal mb-1 sm:mb-0 text-sm text-gray-500 ">
              Date
            </dt>
            <dd className="font-medium text-gray-900 text-sm sm:text-end">
              {bookingData.date}
            </dd>
          </dl>

          <dl className="sm:flex items-center justify-between gap-4">
            <dt className="font-normal mb-1 sm:mb-0 text-gray-500 text-sm">
              Time Slot
            </dt>
            <dd className="font-medium text-gray-900  sm:text-end text-sm">
              {bookingData.fromTime} - {bookingData.toTime}
            </dd>
          </dl>
          <dl className="sm:flex items-center justify-between gap-4">
            <dt className="font-normal mb-1 text-sm sm:mb-0 text-gray-500 ">
              Payment Method
            </dt>
            <dd className="font-medium text-gray-900 text-sm  sm:text-end">
              {bookingData.paymentMethod}
            </dd>
          </dl>

          <dl className="sm:flex items-center justify-between gap-4">
            <dt className="font-normal mb-1 sm:mb-0 text-gray-500 text-sm">
              Address
            </dt>
            <dd className="font-medium text-gray-900  sm:text-end text-sm">
              {bookingData.postalCode} {bookingData.address} {bookingData.city}{' '}
              {bookingData.state}
            </dd>
          </dl>
          <dl className="sm:flex items-center justify-between gap-4">
            <dt className="font-normal mb-1 sm:mb-0 text-gray-500 text-sm">
              Phone
            </dt>
            <dd className="font-medium text-gray-900  sm:text-end">
              {bookingData.phone}
            </dd>
          </dl>
          <dl className="sm:flex items-center justify-between gap-4">
            <dt className="font-normal mb-1 sm:mb-0 text-gray-500 text-sm">
              Total
            </dt>
            <dd className="font-medium text-gray-900  sm:text-end">
              ${bookingData.total}
            </dd>
          </dl>
        </div>

        <div className="space-y-4">
          {/* Add Review Button */}
          <div className="flex justify-center">
            <CustomButton
              label="Add Review"
              className="mt-4"
              color="success"
              radius="sm"
              size="md"
              variant="solid"
              onPress={handleAddReview}
              startContent={
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                  />
                </svg>
              }
            />
          </div>

          {/* Navigation Buttons */}
          <div className="flex flex-initial justify-between items-center gap-4">
            <CustomButton
              fullWidth={true}
              label="Go to Home"
              className="mt-5"
              color="primary"
              radius="sm"
              size="md"
              variant="flat"
              onPress={() => navigateToLink('calender')}
            />

            <CustomButton
              fullWidth={true}
              label="Go to Dashboard"
              className="mt-5"
              color="primary"
              radius="sm"
              size="md"
              onPress={() => navigateToLink('dashboard')}
            />
          </div>
        </div>
      </div>

      {/* Review Form Modal */}
      <ReviewForm
        isOpen={showReviewForm}
        onClose={() => setShowReviewForm(false)}
        onSubmit={handleSubmitReview}
        providerId={bookingData?.providerId}
        serviceId={bookingData?.serviceId}
        bookingId={bookingData?.bookingId || bookingData?.id}
        serviceName={bookingData?.serviceName}
        isEdit={false}
      />
    </div>
  );
};

export default ConfirmationStep;
