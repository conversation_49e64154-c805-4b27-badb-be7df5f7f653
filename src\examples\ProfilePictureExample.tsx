import React, { useState, useEffect } from 'react';
import { Card, CardBody, Divider } from '@heroui/react';
import { useAuth } from 'react-oidc-context';
import { ProfilePictureUpload, ProfilePictureDisplay } from '../feature-module/components/ProfilePicture';
import { 
  getUserProfileWithPicture, 
  ProfilePictureUploadResult,
  getProfilePictureUrlFromName,
  getFallbackProfilePicture
} from '../service/profilePictureService';

/**
 * Example component demonstrating how to use the Profile Picture system
 * This shows various use cases and integration patterns
 */
const ProfilePictureExample: React.FC = () => {
  const auth = useAuth();
  const [userData, setUserData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // Get user ID from auth context
  const getUserId = () => {
    return auth.user?.profile?.preferred_username || 
           auth.user?.profile?.sub || 
           auth.user?.profile?.email;
  };

  // Load user data on component mount
  useEffect(() => {
    const loadUserData = async () => {
      const userId = getUserId();
      if (!userId) return;

      try {
        setLoading(true);
        const profile = await getUserProfileWithPicture(userId);
        setUserData(profile);
      } catch (error) {
        console.error('Error loading user profile:', error);
      } finally {
        setLoading(false);
      }
    };

    if (auth.isAuthenticated) {
      loadUserData();
    }
  }, [auth.isAuthenticated]);

  // Handle successful profile picture upload
  const handleUploadSuccess = (result: ProfilePictureUploadResult) => {
    console.log('Upload successful:', result);
    
    // Update local user data
    setUserData((prev: any) => ({
      ...prev,
      profileImage: result.imageName,
      profileImageUrl: result.fullUrl
    }));
  };

  // Handle successful profile picture deletion
  const handleDeleteSuccess = () => {
    console.log('Profile picture deleted successfully');
    
    // Clear profile image from local user data
    setUserData((prev: any) => ({
      ...prev,
      profileImage: '',
      profileImageUrl: ''
    }));
  };

  if (!auth.isAuthenticated) {
    return (
      <Card className="max-w-md mx-auto mt-8">
        <CardBody className="text-center">
          <p>Please log in to view profile picture examples.</p>
        </CardBody>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card className="max-w-md mx-auto mt-8">
        <CardBody className="text-center">
          <p>Loading user profile...</p>
        </CardBody>
      </Card>
    );
  }

  const userId = getUserId();

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <h1 className="text-3xl font-bold text-center mb-8">Profile Picture System Examples</h1>

      {/* Example 1: Full Profile Picture Upload Component */}
      <Card>
        <CardBody>
          <h2 className="text-xl font-semibold mb-4">1. Complete Profile Picture Upload</h2>
          <p className="text-gray-600 mb-4">
            This shows the full upload component with upload, delete, and display functionality.
          </p>
          
          <div className="flex justify-center">
            <ProfilePictureUpload
              userId={userId || ''}
              currentProfileImage={userData?.profileImage}
              userName={userData?.name}
              onUploadSuccess={handleUploadSuccess}
              onDeleteSuccess={handleDeleteSuccess}
              size="lg"
              className="text-center"
            />
          </div>
        </CardBody>
      </Card>

      <Divider />

      {/* Example 2: Display Only Components */}
      <Card>
        <CardBody>
          <h2 className="text-xl font-semibold mb-4">2. Display Only Components</h2>
          <p className="text-gray-600 mb-4">
            These show different sizes and styles for displaying profile pictures.
          </p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 items-center">
            <div className="text-center">
              <ProfilePictureDisplay
                profileImage={userData?.profileImage}
                userName={userData?.name}
                size="sm"
                showBorder={true}
              />
              <p className="text-sm mt-2">Small (sm)</p>
            </div>
            
            <div className="text-center">
              <ProfilePictureDisplay
                profileImage={userData?.profileImage}
                userName={userData?.name}
                size="md"
                showBorder={true}
              />
              <p className="text-sm mt-2">Medium (md)</p>
            </div>
            
            <div className="text-center">
              <ProfilePictureDisplay
                profileImage={userData?.profileImage}
                userName={userData?.name}
                size="lg"
                showBorder={true}
              />
              <p className="text-sm mt-2">Large (lg)</p>
            </div>
            
            <div className="text-center">
              <ProfilePictureDisplay
                profileImage={userData?.profileImage}
                userName={userData?.name}
                size="lg"
                showBorder={true}
              />
              <p className="text-sm mt-2">Extra Large (xl)</p>
            </div>
          </div>
        </CardBody>
      </Card>

      <Divider />

      {/* Example 3: Different Fallback Scenarios */}
      <Card>
        <CardBody>
          <h2 className="text-xl font-semibold mb-4">3. Fallback Scenarios</h2>
          <p className="text-gray-600 mb-4">
            These show how the system handles missing profile pictures with different fallbacks.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <ProfilePictureDisplay
                profileImage=""
                userName="John Doe"
                size="lg"
                showBorder={true}
              />
              <p className="text-sm mt-2">With Name Fallback</p>
            </div>
            
            <div className="text-center">
              <ProfilePictureDisplay
                profileImage=""
                userName=""
                size="lg"
                showBorder={true}
              />
              <p className="text-sm mt-2">No Name (Default)</p>
            </div>
            
            <div className="text-center">
              <ProfilePictureDisplay
                profileImageUrl={getFallbackProfilePicture("Jane Smith")}
                userName="Jane Smith"
                size="lg"
                showBorder={true}
              />
              <p className="text-sm mt-2">Generated Avatar</p>
            </div>
          </div>
        </CardBody>
      </Card>

      <Divider />

      {/* Example 4: Integration Examples */}
      <Card>
        <CardBody>
          <h2 className="text-xl font-semibold mb-4">4. Integration Examples</h2>
          <p className="text-gray-600 mb-4">
            Examples of how to integrate profile pictures in different UI contexts.
          </p>
          
          {/* Comment/Review Style */}
          <div className="space-y-4">
            <div className="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
              <ProfilePictureDisplay
                profileImage={userData?.profileImage}
                userName={userData?.name}
                size="md"
                showBorder={true}
              />
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="font-semibold">{userData?.name || 'User'}</span>
                  <span className="text-sm text-gray-500">2 hours ago</span>
                </div>
                <p className="text-gray-700 mt-1">
                  This is an example of how profile pictures look in a comment or review context.
                </p>
              </div>
            </div>

            {/* Sidebar Style */}
            <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg">
              <ProfilePictureDisplay
                profileImage={userData?.profileImage}
                userName={userData?.name}
                size="lg"
                showBorder={true}
              />
              <div>
                <h3 className="font-semibold">{userData?.name || 'User'}</h3>
                <p className="text-sm text-gray-600">{userData?.email}</p>
                <p className="text-xs text-green-600">● Online</p>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Debug Information */}
      <Card>
        <CardBody>
          <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
          <div className="bg-gray-100 p-4 rounded-lg">
            <pre className="text-sm overflow-x-auto">
              {JSON.stringify({
                userId: userId,
                profileImage: userData?.profileImage,
                profileImageUrl: userData?.profileImage ? getProfilePictureUrlFromName(userData.profileImage) : null,
                userName: userData?.name,
                userEmail: userData?.email
              }, null, 2)}
            </pre>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default ProfilePictureExample;
