import React from 'react';
import { Avatar } from '@heroui/react';
import { getFallbackProfilePicture, getProfilePictureUrlFromName } from '../../../service/profilePictureService';

interface ProfilePictureDisplayProps {
  profileImage?: string; // Image name stored in database
  profileImageUrl?: string; // Full URL (if already available)
  userName?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showBorder?: boolean;
  isClickable?: boolean;
  onClick?: () => void;
}

const ProfilePictureDisplay: React.FC<ProfilePictureDisplayProps> = ({
  profileImage,
  profileImageUrl,
  userName,
  size = 'md',
  className = '',
  showBorder = false,
  isClickable = false,
  onClick
}) => {
  // Determine the image URL to use
  const getImageUrl = (): string => {
    // If full URL is provided, use it
    if (profileImageUrl) {
      return profileImageUrl;
    }
    
    // If image name is provided, convert to full URL
    if (profileImage) {
      return getProfilePictureUrlFromName(profileImage);
    }
    
    // Fallback to generated avatar
    return getFallbackProfilePicture(userName);
  };

  const imageUrl = getImageUrl();

  // Size mapping for Avatar component
  const sizeMap = {
    sm: 'sm',
    md: 'md',
    lg: 'lg'
  } as const;

  return (
    <Avatar
      src={imageUrl}
      alt={userName ? `${userName}'s profile picture` : 'Profile picture'}
      size={sizeMap[size]}
      className={`
        ${showBorder ? 'ring-2 ring-primary ring-offset-2' : ''}
        ${isClickable ? 'cursor-pointer hover:opacity-80 transition-opacity' : ''}
        ${className}
      `}
      onClick={isClickable ? onClick : undefined}
      fallback={
        <div className="flex items-center justify-center w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 text-white font-semibold">
          {userName ? userName.charAt(0).toUpperCase() : 'U'}
        </div>
      }
    />
  );
};

export default ProfilePictureDisplay;
